<?php

namespace App\Handlers\WeeklyAttendances;

use App\Handlers\HandlerHelper;
use App\Http\Resources\UnVerifiedAttendanceResource;
use App\Http\Resources\VerifiedAttendanceResource;
use App\Models\Cico;
use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Models\Timecard;

class AttendanceEntriesHandler
{
    use HandlerHelper;

    public function handleEntries($attendances, $request): array
    {
        $entries = [];
        foreach ($attendances as $employeeId => $employeeAttendances) {
            $weekDates = $this->generateDaysFromTwoDates($request['start_date'], $request['end_date']);

            $this->handleEmployeeInfo($employeeId, $entries);
            foreach ($employeeAttendances as $date => $employeeAttendance) {
                $weekDates[$date] = true;
                $this->formatAttendanceData($employeeAttendance, $employeeId, $date, $entries);
            }

            foreach ($weekDates as $date => $value) {
                if (! $value) {
                    $entries[$employeeId]['entries'][] = $this->createEntry($date);
                }
            }

            $this->sortEntryByDate($employeeId, $entries);
        }

        return array_values($entries);
    }

    protected function formatAttendanceData($employeeAttendance, $employeeId, $date, &$entries): void
    {
        $verified_attendance = [];
        $cicos = [];
        $rest = false;
        foreach ($employeeAttendance as $attendance) {
            if ($attendance instanceof Timecard) {
                $verified_attendance[] = new VerifiedAttendanceResource($attendance);
            } elseif ($attendance instanceof Cico) {
                $cicos[] = new UnVerifiedAttendanceResource($attendance);
            } elseif ($attendance instanceof EmployeeLeaveRequest) {
                $rest = true;
            }
        }

        $entries[$employeeId]['entries'][] = $this->createEntry($date, $rest, $verified_attendance, $cicos);
    }

    protected function handleEmployeeInfo($employeeId, &$entries): void
    {
        $employee = Employee::find($employeeId);
        $entries[$employee->id]['employee_info'] = [
            'id' => $employee->id,
            'first_name' => $employee->first_name,
            'second_name' => $employee->second_name,
            'employee_number' => $employee->employee_number,
            'branch_name' => $employee->branch->name,
            'title' => [
                'name' => $employee->title->name,
                'color' => $employee->title->color,
            ],
        ];
    }

    public function createEntry($date, $rest = null, $verified_attendance = [], $cicos = [])
    {
        return [
            'date' => $date,
            'verified_attendance' => $verified_attendance,
            'cicos' => $cicos,
            'rest' => $rest,
        ];
    }
}
