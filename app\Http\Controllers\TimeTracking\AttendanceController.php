<?php

namespace App\Http\Controllers\TimeTracking;

use App\DomainData\AttendanceDto;
use App\Http\Controllers\Controller;
use App\Http\Resources\AttendanceResource;
use App\Http\Resources\VerifiedAttendanceCollection;
use App\Models\Attendance;
use App\Repositories\EmployeeRepository;
use App\Repositories\Repository;
use App\Services\LeaveManagement\BusinessServices\GetLeavesFiltersService;
use App\Services\TimeTracking\BusinessServices\ClockInService;
use App\Services\TimeTracking\BusinessServices\ClockOutService;
use App\Services\TimeTracking\BusinessServices\CreateManualTimeCardService;
use App\Services\TimeTracking\BusinessServices\ExportAttendanceAndLeavesService;
use App\Services\TimeTracking\BusinessServices\ExportAttendanceService;
use App\Services\TimeTracking\BusinessServices\getFiltersService;
use App\Services\TimeTracking\BusinessServices\GetTimecardsFilterService;
use App\Services\TimeTracking\CrudServices\AttendanceCrudService;
use App\Services\TimeTracking\CrudServices\CicoCrudService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use stdClass;

class AttendanceController extends Controller
{
    use AttendanceDto;

    private EmployeeRepository $employeeRepository;

    private Repository $cicoRepository;

    private Repository $attendanceRepository;

    private Repository $attendanceSettingRepository;

    public function __construct(
        private ClockInService $clockInService,
        private ClockOutService $clockOutService,
        private getFiltersService $getFiltersService,
        private CreateManualTimeCardService $createManualTimeCardService,
        private AttendanceCrudService $attendanceCrudService,
        private ExportAttendanceService $exportAttendanceService,
        private GetLeavesFiltersService $getLeavesFiltersService,
        private GetTimecardsFilterService $getTimecardsFilterService,
        private ExportAttendanceAndLeavesService $exportAttendanceAndLeavesService,
        private CicoCrudService $cicoCrudService
    ) {
        $this->employeeRepository = new EmployeeRepository('Employee');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
    }

    public function manualCico(array $request, stdClass $output): void
    {
        $rules = [
            'slot_id' => 'required',
            'employee_id' => 'required',
            'clock_in_time' => 'required|date_format:Y-m-d H:i:s',
            'clock_out_time' => 'required|date_format:Y-m-d H:i:s',
            'clock_in_branch' => 'integer',
            'clock_out_branch' => 'integer',
        ];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request = $validator->validate();
        $request['by_create_manual_card'] = true;
        $clockInData = $this->prepareClockInData($request);
        $clockOutData = $this->prepareClockOutData($request);
        $this->clockInService->perform($clockInData, $output);
        if (isset($output->Error)) {
            return;
        }
        if (! isset($output->cico->id)) {
            return;
        }
        $clockOutData['clock_in_id'] = $output->cico->id;
        $this->clockOutService->perform($clockOutData, $output);
    }

    public function prepareClockInData(array $request): array
    {
        return [
            'slot_id' => $request['slot_id'],
            'employee_id' => $request['employee_id'],
            'branch_id' => $request['clock_in_branch'] ?? null,
            'is_manual_action' => true,
            'manual_action_time' => $request['clock_in_time'],
            // 'by_create_manual_card' => true
        ];
    }

    public function prepareClockOutData(array $request): array
    {
        return [
            'slot_id' => $request['slot_id'],
            'employee_id' => $request['employee_id'],
            'branch_id' => $request['clock_out_branch'] ?? null,
            'is_manual_action' => true,
            'manual_action_time' => $request['clock_out_time'],
            // 'by_create_manual_card' => true
        ];
    }

    public function clockIn(array $request, stdClass &$output): void
    {
        $rules = $this->getRules(['slot_id', 'employee_id', 'branch_id', 'lat', 'long', 'notes']);
        $rules = $this->getAdditionalCicoRules($rules);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $request['by_create_manual_card'] = false;
        if (! Arr::get($request, 'is_manual_action', false)) {
            $this->createAutoClockOutIfExists();
        }
        $this->clockInService->perform($request, $output);
    }

    public function clockOut(array $request, stdClass &$output): void
    {

        $rules = $this->getRules(['slot_id', 'clock_in_id', 'branch_id', 'employee_id', 'lat', 'long', 'notes'], $request);
        $rules = $this->getAdditionalCicoRules($rules);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $request['by_create_manual_card'] = false;

        $this->clockOutService->perform($request, $output);
    }

    private function isValidFilters(array &$request, stdClass &$output): bool
    {
        $rules = $this->getFilterRules();

        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        if (! isset($request['order_by'])) {
            $request['order_by'] = config('globals.DEFAULT_ORDER_BY');
        }

        if (! isset($request['order_by_type'])) {
            $request['order_by_type'] = config('globals.DEFAULT_ORDER_BY_TYPE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        $request = $validator->validate();

        return true;
    }

    public function getByFilters(array $request, stdClass &$output): void
    {
        if (! $this->isValidFilters($request, $output)) {
            return;
        }
        $this->getFiltersService->perform($request, $output);

        $output->attendances = AttendanceResource::collection($output->attendances)->response()->getData(true);
    }

    public function getFiltersCount(array $request, stdClass &$output): void
    {
        $rules = $this->getFilterCountRules();
        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        if (! isset($request['order_by'])) {
            $request['order_by'] = config('globals.DEFAULT_ORDER_BY');
        }

        if (! isset($request['order_by_type'])) {
            $request['order_by_type'] = config('globals.DEFAULT_ORDER_BY_TYPE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->getFiltersService->getFiltersKeysAndCount($request, $output);
    }

    public function createManualTimeCard(array $request, stdClass &$output): void
    {
        $rules = $this->getCreateManualTimecardRules();
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        $request['by_create_manual_card'] = true;
        if (! isset($request['clock_in_branch_id'], $request['clock_out_branch_id'])) {
            $request['clock_in_branch_id'] = $this->employeeRepository->find($request['employee_id'])->branch_id;
            $request['clock_out_branch_id'] = $this->employeeRepository->find($request['employee_id'])->branch_id;
        }
        $this->createManualTimeCardService->perform($request, $output);
    }

    public function deleteManualTimeCard(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        $this->attendanceCrudService->deleteManualTimeCard($request, $output);
    }

    public function getById(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['related_objects'] = ['employee.title', 'branch', 'shift', 'attendanceTags', 'attendanceDeductions.employeeApproves.title', 'attendanceOvertimes.employeeApproves.title'];
        $request['related_objects_count'] = [];

        $this->attendanceCrudService->getById($request, $output);
    }

    public function isValidDownloadAttendanceAndLeaves(array &$request, stdClass &$output): bool
    {
        $attendanceTags = implode(',', array_values(config('globals.ATTENDANCE_TAGS')));
        $rules = [
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d|after_or_equal:start_date',
            'leaves_statuses' => 'array',
            'leaves_statuses.*' => 'string|in:pending,approved,rejected,cancelled,in_process',
            'page' => 'integer|min:1',
            'page_size' => 'integer|min:0', // 0 means no pagination
            'search_value' => 'min:1|max:30',
            'branch_ids' => 'array',
            'branch_ids.*' => 'integer',
            'title_ids' => 'array',
            'title_ids.*' => 'integer',
            'department_ids' => 'array',
            'department_ids.*' => 'integer',
            'filter_keys' => 'array',
            'filter_keys.*' => 'string',
            // 'attendance_filter_key' => 'string|in:' . $attendanceTags,
            'order_by' => 'string|in:from,created_at', // from is the leave start date ,, created_at is when the request was requested
            'order_by_type' => 'string|in:asc,desc',
        ];

        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        if (! isset($request['order_by'])) {
            $request['order_by'] = 'from';
        }

        if (! isset($request['order_by_type'])) {
            $request['order_by_type'] = config('globals.DEFAULT_ORDER_BY_TYPE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        $request = $validator->validate();

        return true;
    }

    public function downloadAttendances($request, stdClass &$output): void  // temporarily change the function name as to not change the route on web client
    {
        if (! $this->isValidDownloadAttendanceAndLeaves($request, $output)) {
            return;
        }

        $request['page_size'] = 0;

        $timecardRequest = $request;

        // if(isset($request['attendance_filter_key']))
        //     $timecardRequest['filter_key'] = $request['attendance_filter_key'];

        $leavesRequest = $request;
        $leavesRequest['from_date'] = $request['start_date'];
        $leavesRequest['to_date'] = $request['end_date'];
        $leavesRequest['statuses'] = ['approved'];

        // if(isset($request['leaves_statuses']))
        //     $leavesRequest['statuses'] = $request['leaves_statuses'];

        $this->getLeavesFiltersService->perform($leavesRequest, $output);

        $timecardRequest['approved_only'] = true;
        $timecardRequest['with_area'] = true;
        $this->getTimecardsFilterService->perform($timecardRequest, $output);

        if (isset($output->Error)) {
            return;
        }

        $exportRequest['leaves'] = $output->leaves->data ?? [];
        $exportRequest['verified_attendance'] = (new VerifiedAttendanceCollection($output->verified_attendance ?? []))->response()->getData()?->data;
        $exportOutput = new stdClass;
        $this->exportAttendanceAndLeavesService->perform($exportRequest, $exportOutput);

        $output->return_link = $exportOutput->return_link;
    }

    public function deleteAttendance($request, $output): void
    {
        $rules = ['attendance_id' => 'required|exists:attendances,id'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->attendanceCrudService->deleteAttendance($request, $output);
    }

    public function createAutoClockOutIfExists()
    {
        $employeeId = config('globals.user')->employee_id;
        $companyId = config('globals.user')->company_id;
        $today = date('Y-m-d');
        $clockIn = $this->cicoRepository->getClockInsWithoutOutForEmployeeInDate($today, $employeeId)?->first();
        $automatedClockOutSetting = $this->attendanceSettingRepository->findAttendanceSetting('automated_clock_out', $companyId);
        if (isset($clockIn) && isset($automatedClockOutSetting)) {
            $isAutoByEmployee = true;
            if ($clockIn->status == 'unverified') {
                $this->cicoCrudService->automateClockOutForUnverifiedClockIn($clockIn, $automatedClockOutSetting, $isAutoByEmployee);
            } else {
                $attendance = $this->attendanceRepository->findByCondition('ci_id', '=', $clockIn->id);
                $this->cicoCrudService->automateClockOutForVerifiedClockIn($clockIn, $attendance, $automatedClockOutSetting, $isAutoByEmployee);
            }
        }
    }
}
