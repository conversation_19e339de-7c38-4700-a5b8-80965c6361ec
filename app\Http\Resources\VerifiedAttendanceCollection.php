<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class VerifiedAttendanceCollection extends ResourceCollection
{
    protected $authEmployee;

    /**
     * Create a new collection instance.
     *
     * @param mixed $resource
     */
    public function __construct($resource)
    {
        parent::__construct($resource);

        // Fetch the authenticated user's employee once
        $this->authEmployee = auth()->user()->employee;
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->collection->map(function ($item) use ($request) {
            return (new VerifiedAttendanceResource($item, $this->authEmployee))->toArray($request);
        });
    }
}
