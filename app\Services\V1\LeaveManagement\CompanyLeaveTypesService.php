<?php

namespace App\Services\V1\LeaveManagement;

use App\Exceptions\UnprocessableException;
use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Repositories\NewCompanyLeaveTypePolicyRepository;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\EmployeeRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Services\BaseService;
use App\Services\LeaveManagement\FillEmployeeBalancesService as OldFillEmployeeBalancesService;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use App\Traits\GenerateUuid;
use App\Traits\V1\Leaves\LeavesHelper;

class CompanyLeaveTypesService extends BaseService
{
    use GenerateUuid, LeavesHelper;

    private $companyLeaveType;

    private $oldEmployeeLeaveBalanceRepository;

    private $employeeLeaveBalanceRepository;

    public function __construct(NewCompanyLeaveTypeRepository               $newCompanyLeaveTypeRepository,
                                private NewCompanyLeaveTypePolicyRepository $newCompanyLeaveTypePolicyRepository,
                                private EmployeeLeaveRequestRepository      $newEmployeeLeaveRequestRepository,
                                private CompanyDefaultLeaveTypesRepository  $companyDefaultLeaveTypesRepository,
                                private SalaryComponentsCategoriesService   $salaryComponentsCategoriesService,
                                private OldFillEmployeeBalancesService      $oldFillEmployeeBalancesService,
                                private FillEmployeeBalancesService         $fillEmployeeBalancesService,
                                private EmployeeRepository                  $employeeRepository,
                                protected SystemSettingRepository           $systemSettingRepository,)
    {
        parent::__construct($newCompanyLeaveTypeRepository);
        $this->oldEmployeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->employeeLeaveBalanceRepository = new EmployeeLeaveBalancesRepository;
    }

    public function updateWithPolicy($id, $data)
    {
        $companyLeaveType = $this->repository->findOrFail($id);

        // Check if this is a preset leave type
        $presetLeaveIds = [auth()->user()->company->annualLeaveType->id,
            auth()->user()->company->sickLeaveType->id,
            auth()->user()->company->restDayLeaveType->id];

        // If this is a preset leave type, prevent changing names
        if (in_array($companyLeaveType->id, $presetLeaveIds)) {
            // Remove name fields from the data
            unset($data['name_en'], $data['name_ar'], $data['name']);
        }

        if (isset($data['partial_leave_allowed'])) {
            $partialLeaveToggle = $data['partial_leave_allowed'];
            unset($data['partial_leave_allowed']);

            $setting = $this->systemSettingRepository->findByKey('key', 'partial_leave_enabled')->first();
            if (!isset($setting)) {
                $this->systemSettingRepository->add([
                    'key' => 'partial_leave_enabled',
                    'value' => $partialLeaveToggle,
                    'company_id' => auth()->user()->company_id,
                    'as_of_date' => now()->format('Y-m-d'),
                    'updated_by' => auth()->user()->id,
                ]);
            } else {
                $setting->value = $partialLeaveToggle;
                $setting->updatedBy()->associate(auth()->user()->employee);
                $setting->save();
            }
        }

        $keysLeaveTypeOnly = ['gender', 'balance_period', 'name_en', 'name_ar', 'name', 'leave_deduction_percentage'];
        $companyLeaveTypeData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
            return in_array($key, $keysLeaveTypeOnly);
        }, ARRAY_FILTER_USE_KEY);

        $companyLeaveType->update($companyLeaveTypeData);

        if ($companyLeaveType->leave_deduction_percentage > 0) {
            $this->salaryComponentsCategoriesService->setSickLeaveDeductionComponent();
        }

        $companyLeaveTypePolicyData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
            return !in_array($key, $keysLeaveTypeOnly);
        }, ARRAY_FILTER_USE_KEY);

        $oldBalance = $companyLeaveType->companyLeaveTypePolicy->base_balance;
        $oldISProratedMonthly = $companyLeaveType->companyLeaveTypePolicy->prorated_monthly;
        $companyLeaveType->companyLeaveTypePolicy->update($companyLeaveTypePolicyData);

        $balances = $companyLeaveType->companyLeaveTypePolicy->employeeLeaveBalances()->withWhereHas('employee')->whereDate('end', '>=', now()->format('Y-m-d'))->get();

        if (isset($data['prorated_monthly']) && $data['prorated_monthly'] != $oldISProratedMonthly) {
            $companyLeaveType->refresh();
            if ($data['prorated_monthly'] && $companyLeaveType->balance_period != config('globals.BALANCE_PERIODS.CALENDAR_YEAR')) {
                throw new UnprocessableException('Prorated monthly can only be set for leave types with calendar year balance period');
            }
            $this->changeIsProratedPolicy($balances);

        }
        if (isset($data['titles'])) {
            $oldTitles = $companyLeaveType->companyLeaveTypePolicy->titles;
            $deletedTitles = array_diff($oldTitles->pluck('id')->toArray(), $data['titles']);

            if (count($deletedTitles)) {
                $this->oldEmployeeLeaveBalanceRepository->deleteEmployeeLeaveBalancesByTitlesAndPolicy($deletedTitles, $companyLeaveType->companyLeaveTypePolicy->id);
            }

            $companyLeaveType->companyLeaveTypePolicy->titles()->sync($data['titles']);
        }
        if (isset($companyLeaveTypePolicyData['base_balance']) && $companyLeaveTypePolicyData['base_balance'] != $oldBalance) {
            $newBalance = $companyLeaveTypePolicyData['base_balance'];

            foreach ($balances as $balanceEntity) {
                $balanceEntity->balance = max(0, $balanceEntity->balance + ($newBalance - $oldBalance));
                $balanceEntity->save();
            }

        }
        if ($companyLeaveType->id != config('globals.company')->annualLeaveType->id) {
            $nonTerminatedEmployees = $this->employeeRepository->getNonTerminatedEmployees($data['titles'] ?? null);
            foreach ($nonTerminatedEmployees as $employee) {
                dispatch(new FillEmployeeBaseBalancesJob([$employee], [$employee->company_id], $companyLeaveType->id))
                    ->onConnection(config('globals.LEAVE_BALANCE_FILL.CONNECTION'))
                    ->onQueue(config('globals.LEAVE_BALANCE_FILL.QUEUE'));
            }
        }

    }

    private function changeIsProratedPolicy($balances)
    {

        $approvdLeaves = $this->newEmployeeLeaveRequestRepository->getSumApprovedLeavesInRangeGroupedByEmployee($balances->first()->start, $balances->first()->end, $balances->first()->company_leave_type_policy_id)->keyBy('employee_id');

        foreach ($balances as $balance) {
            $baseBalance = $this->getBaseBalance($balance) + $balance->transferred_balance;
            $employee = $balance->employee;
            $netQuantitiesSum = $approvdLeaves[$employee->id]?->total ?? 0;
            $aggregatedBalance = ($baseBalance) - $netQuantitiesSum;
            $this->employeeLeaveBalanceRepository->update($balance->id, ['balance' => max(0, $aggregatedBalance)]);
        }
    }

    public function addWithPolicy($data)
    {

        $titles = $data['titles'];
        unset($data['titles']);
        $data['uuid'] = $this->Uuid7();
        $keysLeaveTypeOnly = ['gender', 'balance_period', 'name_en', 'name_ar', 'name', 'leave_deduction_percentage', 'uuid'];

        $companyLeaveTypeData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
            return in_array($key, $keysLeaveTypeOnly);
        }, ARRAY_FILTER_USE_KEY);

        $companyLeaveType = $this->repository->add($companyLeaveTypeData);

        $companyLeaveTypePolicyData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
            return !in_array($key, $keysLeaveTypeOnly);
        }, ARRAY_FILTER_USE_KEY);

        $companyLeaveTypePolicyData['company_leave_type_id'] = $companyLeaveType->id;

        $companyLeaveTypePolicy = $this->newCompanyLeaveTypePolicyRepository->add($companyLeaveTypePolicyData);

        $companyLeaveTypePolicy->titles()->attach($titles);

        $employees = $this->employeeRepository->getNonTerminatedEmployees();
        foreach ($employees as $employee) {
            dispatch(new FillEmployeeBaseBalancesJob([$employee], [$employee->company_id], $companyLeaveType->id));
        }
    }

    public function delete($id)
    {

        $companyLeaveType = $this->repository->findOrFail($id);

        $presetLeaveIds = [config('globals.company')->annual_leave_id, // TODO replace all preset leave ids with the leave types relations in company model
            config('globals.company')->sick_leave_id, config('globals.company')->rest_day_leave_id];

        if (in_array($companyLeaveType->id, $presetLeaveIds)) {
            throw new UnprocessableException('This leave type is preset and cannot be deleted');
        }

        $leaveRequestsWithType = $this->newEmployeeLeaveRequestRepository->findByKey('company_leave_type_id', $companyLeaveType->id);

        if (isset($leaveRequestsWithType) && count($leaveRequestsWithType)) {
            throw new UnprocessableException('This leave type is used in leave requests');
        }

        $companyLeaveType->companyLeaveTypePolicy->titles()->detach();

        $companyLeaveType->companyLeaveTypePolicy()->delete();

        $companyLeaveType->employeeLeaveBalances()->delete();

        $companyLeaveType->delete();
    }
}
