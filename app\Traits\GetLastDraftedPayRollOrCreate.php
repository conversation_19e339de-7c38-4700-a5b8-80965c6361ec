<?php

namespace App\Traits;

use App\Exceptions\UnprocessableException;
use App\Models\Payroll;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\PayrollUtil;
use Carbon\Carbon;

trait GetLastDraftedPayRollOrCreate
{
    use PayrollHelper;

    public function getCurrentPayroll()
    {
        $payrollsRepository = app(PayrollsRepository::class);
        $lastDraftedPayroll = $payrollsRepository->getLastDraftedPayroll();

        if (is_null($lastDraftedPayroll)) {
            $monthlyClosingDay = $this->getMonthlyClosingDay();
            if (! isset($monthlyClosingDay)) {
                throw new UnprocessableException('Monthly closing day not set');
            }

            // Get the start and end date of the payroll period that covers the current date
            $end = date('Y-m-d', strtotime(date('Y-m-'.$monthlyClosingDay).(date('d') <= $monthlyClosingDay ? '' : ' +1 month')));
            $start = $this->getPayrollMonthStartBasedOnMonthEnd($end);

            $payrollForDate = $this->payrollsRepository->payrollCoversDate(date('Y-m-d'));

            if (! $payrollForDate) {
                return $payrollsRepository->add([
                    'company_id' => auth()->user()->company_id,
                    'month' => date('m', strtotime($end)),
                    'year' => date('Y', strtotime($end)),
                    'status' => PayrollUtil::PAYROLL_STATUS['DRAFT'],
                    'start' => $start,
                    'end' => $end,
                ]);
            } else {
                return $payrollForDate;
            }
        }

        return $lastDraftedPayroll;

    }

    public function getPayrollPeriodFromDate($date)
    {

        $monthlyClosingDay = $this->getMonthlyClosingDay($date);
        if (! isset($monthlyClosingDay)) {
            throw new UnprocessableException('Monthly closing day not set');
        }

        $payrollMonth = Carbon::parse($date)->format('m');
        $payrollYear = Carbon::parse($date)->format('Y');

        $payrollStartDate = Carbon::parse($payrollYear.'-'.$payrollMonth.'-'.$monthlyClosingDay)->subMonth()->addDay();
        $payrollEndDate = Carbon::parse($payrollYear.'-'.$payrollMonth.'-'.$monthlyClosingDay);

        if ($payrollEndDate->lessThan(Carbon::parse($date))) {
            $payrollStartDate->addMonth();
            $payrollEndDate->addMonth();
        }

        return ['start' => $payrollStartDate, 'end' => $payrollEndDate];

    }
}
