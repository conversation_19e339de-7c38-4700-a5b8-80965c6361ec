<?php

namespace App\Models;

use App\DomainData\EmployeeDto;
use App\Traits\CompanyRule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

class Employee extends BaseModel
{
    use CompanyRule, EmployeeDto, HasFactory, LogsActivity, SoftDeletes;

    protected $fillable = [];

    protected static $recordEvents = ['created', 'updated', 'deleted'];

    public $relationFilters = [
        ['relation_name' => 'employeeSalary', 'table_name' => 'employee_salaries', 'type' => 'many'],

    ];

    protected $appends = [
        'name',
        'name_en',
        'first_name',
        'first_name_en',
        'second_name',
        'second_name_en',
        'third_name',
        'third_name_en',
        'fourth_name',
        'fourth_name_en',
        'fifth_name',
        'fifth_name_en',
    ];

    public function getNameAttribute()
    {
        $nameAttribute = 'name_'.(config('globals.lang') ?? 'ar');

        return !empty($this->{$nameAttribute}) ? $this->{$nameAttribute} : $this->name_ar;
    }

    public function getNameEnAttribute()
    {
        return !empty($this->attributes['name_en']) ? $this->attributes['name_en'] : $this->name_ar;
    }

    public function getFirstNameAttribute()
    {
        $firstNameAttribute = 'first_name_'.(config('globals.lang') ?? 'ar');
        if (empty($this->{$firstNameAttribute})) {
            return $this->first_name_ar;
        }
        return $this->{$firstNameAttribute};
    }

    public function getSecondNameAttribute()
    {
        $secondNameAttribute = 'second_name_'.(config('globals.lang') ?? 'ar');

        if (empty($this->{$secondNameAttribute})) {
            return $this->second_name_ar;
        }
        return $this->{$secondNameAttribute};
    }

    public function getThirdNameAttribute()
    {
        $thirdNameAttribute = 'third_name_'.(config('globals.lang') ?? 'ar');

        if (empty($this->{$thirdNameAttribute})) {
            return $this->third_name_ar;
        }
        return $this->{$thirdNameAttribute};
    }

    public function getFourthNameAttribute()
    {
        $fourthNameAttribute = 'fourth_name_'.(config('globals.lang') ?? 'ar');

        if (empty($this->{$fourthNameAttribute})) {
            return $this->fourth_name_ar;
        }
        return $this->{$fourthNameAttribute};
    }

    public function getFifthNameAttribute()
    {
        $fifthNameAttribute = 'fifth_name_'.(config('globals.lang') ?? 'ar');

        if (empty($this->{$fifthNameAttribute})) {
            return $this->fifth_name_ar;
        }
        return $this->{$fifthNameAttribute};
    }
    public function getFirstNameEnAttribute() {
        return !empty($this->attributes['first_name_en']) ? $this->attributes['first_name_en'] : $this->first_name_ar;
    }
    public function getSecondNameEnAttribute() {
        return !empty($this->attributes['second_name_en']) ? $this->attributes['second_name_en'] : $this->second_name_ar;
    }
    public function getThirdNameEnAttribute() {
        return !empty($this->attributes['third_name_en']) ? $this->attributes['third_name_en'] : $this->third_name_ar;
    }
    public function getFourthNameEnAttribute() {
        return !empty($this->attributes['fourth_name_en']) ? $this->attributes['fourth_name_en'] : $this->fourth_name_ar;
    }
    public function getFifthNameEnAttribute() {
        return !empty($this->attributes['fifth_name_en']) ? $this->attributes['fifth_name_en'] : $this->fifth_name_ar;
    }

    public function scopeBranch($query)
    {
        $query->where('branch_id', config('globals.branchId'));
    }

    public function scopeActiveEmployees($query, $endDate = null)
    {
        $query->where('status', 'active')
            ->whereHas('employeeInfo', function ($subQuery) use ($endDate) {
                $subQuery->whereNull('termination_date')
                    ->orWhere(function ($q) use ($endDate) {
                        if ($endDate) {
                            $endYearMonth = date('Y-m', strtotime($endDate));
                            $q->whereRaw("DATE_FORMAT(termination_date, '%Y-%m') >= ?", [$endYearMonth]);
                        }
                    });
            });
    }

    public function directManager()
    {
        return $this->belongsTo(Employee::class, 'direct_manager_id');
    }

    public function realtedEmployees()
    {
        return $this->hasMany(Employee::class, 'direct_manager_id');
    }

    public function DefaultRestDays()
    {
        return $this->hasMany(DefaultRestDays::class);
    }

    public function title()
    {
        return $this->belongsTo(Title::class)->withTrashed();
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function branches()
    {
        return $this->belongsToMany(Branch::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class)->withTrashed();
    }

    public function governorate()
    {
        return $this->belongsTo(Governorate::class)->withTrashed();
    }

    public function employeeInsurance()
    {
        return $this->hasOne(EmployeeInsurance::class);
    }

    public function user()
    {
        return $this->hasOne(User::class);
    }

    public function employeeImage()
    {
        return $this->hasMany(EmployeeImage::class);
    }

    public function employeeSalaries()
    {
        return $this->hasMany(EmployeeSalary::class);
    }

    public function employeeSalary()
    {
        return $this->hasOne(EmployeeSalary::class)->latest('as_of_date')->latest('updated_at');
    }

    public function titleShiftEmployees()
    {
        return $this->hasMany(TitleShiftEmployee::class);
    }

    public function attendanceDeductions()
    {
        return $this->hasMany(AttendanceDeduction::class);
    }

    public function attendanceEarlyClockOutDeductions()
    {
        return $this->hasMany(AttendanceEarlyClockOutDeduction::class);
    }

    public function attendanceOvertimes()
    {
        return $this->hasMany(AttendanceOvertime::class);
    }

    public function attendanceTag()
    {
        return $this->hasMany(AttendanceTag::class);
    }

    public function attendance()
    {
        return $this->hasMany(Attendance::class);
    }

    public function attendanceOvertimeApproves()
    {
        return $this->morphedByMany(AttendanceOvertime::class, 'overtime_approves', 'approval_cycles',
            'employee_id', 'approval_id')->withPivot(['company_id', 'status', 'status_date']);
    }

    public function attendanceDeductionApproves()
    {
        return $this->morphedByMany(AttendanceDeduction::class, 'deduction_approves', 'approval_cycles',
            'employee_id', 'approval_id')->withPivot(['company_id', 'status', 'status_date']);
    }

    public function employeeExtraPayrollSettings()
    {
        return $this->hasMany(EmployeeExtraPayrollSetting::class);
    }

    public function monthlyPayroll()
    {
        return $this->hasMany(MonthlyPayroll::class);
    }

    public function employeeRequests()
    {
        return $this->hasMany(EmployeeRequest::class);
    }

    public function employeeLeaveBalances()
    {
        return $this->hasMany(EmployeeLeaveBalance::class);
    }

    public function timecards()
    {
        return $this->hasMany(Timecard::class);
    }

    public function employeeLeaveRequests()
    {
        return $this->hasMany(EmployeeLeaveRequest::class);
    }

    public function employeeMonthlySalaryComponents()
    {
        return $this->hasMany(EmployeeSalaryComponentMonth::class);
    }

    public function cicos()
    {
        return $this->hasMany(Cico::class);
    }

    public function approval()
    {
        return $this->hasMany(ApprovalCycle::class);
    }

    public function employeeInfo()
    {
        return $this->hasOne(EmployeeInfo::class);
    }

    public function employeeChanges()
    {
        return $this->hasMany(EmployeeChange::class);
    }

    public function employeeChangesByMe()
    {
        return $this->hasMany(EmployeeChange::class, 'by_employee_id');
    }

    public function employeeFiles()
    {
        return $this->hasMany(EmployeeFile::class);
    }

    public function profilePicture()
    {
        return $this->morphOne(Attachment::class, 'attachable')
            ->where('attachable_type', 'employee');
    }

    public function faceIdImage()
    {
        return $this->hasOne(EmployeeFaceIdImage::class);
    }

    public function managedDepartments()
    {
        return $this->belongsToMany(Department::class);
    }

    public function managedSubDepartments()
    {
        return $this->belongsToMany(SubDepartment::class);
    }

    public function payrollPolicy()
    {
        return $this->morphOne(PayRollPolicy::class, 'payable')->latest('id');
    }

    public function penalties()
    {
        return $this->hasMany(Penalty::class);
    }

    public function publicHolidaysAttendance()
    {
        return $this->hasMany(PublicHolidaysAttendance::class);
    }

    public function publicHolidaysAttendancePayout()
    {
        return $this->hasMany(PublicHolidaysAttendancePayout::class);
    }

    public function employeeRequsets()
    {
        return $this->hasMany(EmployeeRequest::class);
    }

    public function employeeHireHistory(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(EmployeeHireHistory::class);
    }

    public function latestContract()
    {
        return $this->hasOne(EmployeeContract::class)->orderBy('contract_start_date', 'desc');
    }

    public function publicHolidayAbsences()
    {
        return $this->hasMany(PublicHolidayAbsence::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll();
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        set_impersonate_activity_causer($activity);
    }

    public function anyLocationBalances()
    {
        return $this->hasMany(AnyLocationBalance::class);
    }

    public function terminationRequest()
    {
        return $this->hasMany(TerminationRequest::class)->latest('id');
    }

    public function getLatestTerminateReason()
    {
        $latestTerminationRequest = $this->terminationRequest()->latest()->first();

        return $latestTerminationRequest ? $latestTerminationRequest->terminate_reason : null;
    }

    public function getLatestTerminateType()
    {
        $latestTerminationRequest = $this->terminationRequest()->latest()->first();

        return $latestTerminationRequest ? $latestTerminationRequest->terminate_type : null;
    }

    public function anyLocationBalancesEqualOrGreaterToday()
    {
        return $this->hasMany(AnyLocationBalance::class)
            ->where(function ($q) {
                $q->where('week_start_date', '<=', date('Y-m-d'))
                    ->where('week_end_date', '>=', date('Y-m-d'));
            })->orWhere('week_start_date', '>=', date('Y-m-d'));

    }

    public function activityLogs()
    {
        return $this->morphMany(ActivityLog::class, 'causer', 'causer_type', 'causer_id');
    }

    public function loans()
    {
        return $this->hasMany(Loan::class);
    }

    public function salaryAdvances()
    {
        return $this->hasMany(SalaryAdvance::class);
    }

    public function repaidLeaveBalances()
    {
        return $this->hasMany(RepaidLeaveBalance::class);
    }

    public function employeeTitleChanges()
    {
        return $this->hasMany(EmployeeTitleChange::class);
    }

    public function excusesBalance()
    {
        return $this->hasMany(ExcusesBalance::class);
    }

    public function probationRequest()
    {
        return $this->hasMany(ProbationRequest::class);
    }

    public function employeePayrollSummaries()
    {
        return $this->hasMany(EmployeePayrollSummary::class);
    }

    public function missionRequests()
    {
        return $this->hasMany(MissionRequest::class);
    }

    public function emergencyContacts()
    {
        return $this->hasMany(EmployeeEmergencyContact::class);
    }

    public function latestEmergencyContact()
    {
        return $this->hasOne(EmployeeEmergencyContact::class)->orderBy('id', 'desc');
    }

    public function education()
    {
        return $this->hasMany(EmployeeEducation::class);
    }

    public function latestEducation()
    {
        return $this->hasOne(EmployeeEducation::class)->orderBy('id', 'desc');
    }

    public function contracts()
    {
        return $this->hasMany(EmployeeContract::class);
    }
}
