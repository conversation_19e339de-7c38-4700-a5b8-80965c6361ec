<?php

namespace App\Repositories;

use App\Enums\Missions\MissionsEnum;
use App\FeatureToggles\Unleash;
use App\Models\Timecard;
use App\Traits\QueriesHelper;
use App\Util\AttendanceUtil;
use App\Util\EmployeeUtil;
use App\Util\PayrollUtil;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TimecardRepository extends Repository
{
    use QueriesHelper;

    public function getTimecardForEmployee(int $timecardId, int $employeeId): ?Model
    {
        return $this->getModel->where('id', $timecardId)
            ->where('employee_id', $employeeId)->first();
    }

    public function checkEmployeeTimeCardsToday($employeeId): bool
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereRaw('DATE(`from`) = ?', [Carbon::now()->format('Y-m-d')])
            ->exists();
    }

    public function checkOverlappingTimeCards(int $employeeId, string $fromDate, string $toDate, ?int $slotId = null)
    {
        return $this->getModel->where('employee_id', $employeeId)
            ->whereDoesntHave('entityTags', function ($q) {
                $q->where('tag', '=', 'absent');
            })
            ->where(function ($q) use ($fromDate, $toDate) {
                $q->where(function ($q) use ($fromDate) {
                    $q->where('to', '>=', $fromDate)
                        ->where('from', '<=', $fromDate);
                })->orWhere(function ($q) use ($toDate) {
                    $q->where('to', '>=', $toDate)
                        ->where('from', '<=', $toDate);
                })->orWhere(function ($q) use ($fromDate, $toDate) {
                    $q->where('from', '>=', $fromDate)
                        ->where('to', '<=', $toDate);
                });
            })
            ->when(! is_null($slotId), function ($q) use ($slotId) {
                $q->where('id', '!=', $slotId);
            })
            ->first();
    }

    public function checkOverlappingTimeCardsInUpdate(Timecard $timeCard, string $fromDate, string $toDate): bool
    {
        return $this->getModel
            ->where('employee_id', $timeCard->employee_id)
            ->where('to', '>=', $fromDate)
            ->where('from', '<=', $toDate)
            ->where('id', '!=', $timeCard->id)
            ->exists();
    }

    public function timecardExistsOnDate($employeeId, $date): bool
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->exists();
    }

    public function timecardsInRange($employeeId, $from, $to)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', '>=', $from)
            ->whereDate('from', '<=', $to)
            ->get();
    }

    public function getTimeCardsForWorker(string $startDate, string $endDate, int $employeeId, int $branchId): Collection
    {
        return $this->getModel
            ->with(['shift', 'attendance', 'timecardType:id,name'])
            ->where('employee_id', $employeeId)
            ->where('branch_id', $branchId)
            ->whereDate('to', '>=', $startDate)
            ->whereDate('from', '<=', $endDate)
            ->get();
    }

    public function getTimeCardForWorker(int $employeeId, int $branchId)
    {
        $today = Carbon::now()->toDateString();
        $currentTime = Carbon::now()->toTimeString();
        $attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
        $clockOutDeadline = $attendanceSettingRepository->findAttendanceSetting(AttendanceUtil::CLOCK_OUT_DEADLINE);
        $clockOutDeadlineValue = $clockOutDeadline ? (int) $clockOutDeadline->value ?? 0 : 0;

        DB::enableQueryLog();

        return $this->getModel
            ->with(['shift', 'attendance'])
            ->where('employee_id', $employeeId)
            ->where('branch_id', $branchId)
            ->whereDoesntHave('attendance.clockIn', function ($query) {
                $query->whereNotNull('paired_clock_id');
            })
            ->where(function ($query) use ($today, $currentTime, $clockOutDeadlineValue) {
                // Include shifts that are currently running or have ended after the clock out deadline
                $query->where(function ($subQuery) use ($today, $currentTime, $clockOutDeadlineValue) {
                    $subQuery->where(function ($q) use ($today) {
                        // Running shifts that started at or before today and end after today
                        $q->whereDate('from', '<=', $today)
                            ->whereDate('to', '>', $today);
                    })->orWhere(function ($q) use ($today, $currentTime) {
                        // Running shifts that end today but after the current time
                        $q->whereDate('to', '=', $today)
                            ->whereTime('to', '>=', $currentTime);
                    })
                        ->orWhereHas('attendance', function ($q) use ($clockOutDeadlineValue) {
                            // Shifts that have an attendance record ending before today but after the clock out deadline
                            $q->where('to', '>=', now()->subMinutes($clockOutDeadlineValue)->toDateTimeString());
                        });
                })
                    ->orWhere(function ($subQuery) use ($today, $currentTime) {
                        // Upcoming shifts that start after today or later on today after the current time
                        $subQuery->whereDate('from', '>', $today)
                            ->orWhere(function ($q) use ($today, $currentTime) {
                                $q->whereDate('from', '=', $today)
                                    ->whereTime('from', '>', $currentTime);
                            });
                    });

            })
            ->orderBy('from', 'asc')
            ->get();

    }

    public function getWorkerSchedule(int $employeeId, int $branchId, string $from, string $to)
    {
        return $this->getModel
            ->with(['branch', 'clockInBranch', 'clockOutBranch'])
            ->where('employee_id', $employeeId)
            ->where('branch_id', $branchId)
            ->whereDate('to', '>=', $from)
            ->whereDate('from', '<=', $to)
            ->orderBy('from', 'asc')
            ->get();
    }

    public function getWithAttendanceAndCico($request)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $scopeBranchIds = config('globals.scope_branch_ids');

        $unleash = app(Unleash::class);
        $isEarlyClockoutDeductionEnabled = $unleash->isEarlyClockoutDeductionEnabled();
        $isMissingClockoutDeductionEnabled = $unleash->isMissingClockoutDeductionEnabled();
        $relationships = [
            // Attendance Relationships
            'attendance.clockIn.branch',
            'attendance.clockOut.branch',
            'attendance.clockIn.branchWithTrashed',
            'attendance.clockOut.branchWithTrashed',
            'attendance.entityTags',
            'attendance.attendanceDeductions',
            'attendance.attendanceDeductions.employeeRequests',
            'attendance.attendanceDeductions.employeeRequests.requestedBy',
            'attendance.attendanceDeductions.workflowApprovalCycles',
            'attendance.attendanceDeductions.employeeApproves',
            'attendance.attendanceDeductions.employeeApproves.users', // No need to repeat nested "users" relations
            'attendance.attendanceDeductions.employeeApproves.users.employee', // Combine all "employee" relations
            'attendance.attendanceDeductions.employeeApproves.users.employee.managedDepartments', // User relationships
            'attendance.attendanceDeductions.employeeApproves.users.employee.managedSubDepartments', // User relationships
            'attendance.attendanceDeductions.employeeApproves.users.employee.branches', // User relationships
            'attendance.attendanceDeductions.employeeApproves.users.employee.user', // User relationships
            'attendance.attendanceDeductions.employeeApproves.users.employee.user.customRoles',
            'attendance.attendanceDeductions.employeeApproves.users.employee.user.customRoles.scopes',
            'attendance.attendanceDeductions.employeeApproves.scopes', // Approve scopes
        ];

        if ($isEarlyClockoutDeductionEnabled) {
            $relationships = array_merge($relationships, [
                'attendance.attendanceEarlyClockOutDeduction',
                'attendance.attendanceEarlyClockOutDeduction.employeeRequests',
                'attendance.attendanceEarlyClockOutDeduction.employeeRequests.requestedBy',
                'attendance.attendanceEarlyClockOutDeduction.workflowApprovalCycles',
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves',
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users', // No need to repeat nested "users" relations
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee', // Combine all "employee" relations
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.managedDepartments', // User relationships
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.managedSubDepartments', // User relationships
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.branches', // User relationships
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.user', // User relationships
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.user.customRoles',
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.users.employee.user.customRoles.scopes',
                'attendance.attendanceEarlyClockOutDeduction.employeeApproves.scopes',
            ]);
        }
        if ($isMissingClockoutDeductionEnabled) {
            $relationships[] = 'attendance.missingClockoutDeductions';
        }

        // Add overtime relationships
        $relationships = array_merge($relationships, [
            // Attendance Overtimes
            'attendance.attendanceOvertimes',
            'attendance.attendanceOvertimes.employeeRequests',
            'attendance.attendanceOvertimes.employeeRequests.requestedBy',
            'attendance.attendanceOvertimes.employeeApproves',
            'attendance.attendanceOvertimes.employeeApproves.scopes',
            'attendance.attendanceOvertimes.employeeApproves.users', // Avoid redundant nested "users"
            'attendance.attendanceOvertimes.employeeApproves.users.employee', // Employee relations
            'attendance.attendanceOvertimes.employeeApproves.users.employee.managedDepartments', // Employee relations
            'attendance.attendanceOvertimes.employeeApproves.users.employee.managedSubDepartments', // Employee relations
            'attendance.attendanceOvertimes.employeeApproves.users.employee.branches', // Employee relations
            'attendance.attendanceOvertimes.employeeApproves.users.employee.user', // User relationships
            'attendance.attendanceOvertimes.employeeApproves.users.employee.user.customRoles',
            'attendance.attendanceOvertimes.employeeApproves.users.employee.user.customRoles.scopes',
            'attendance.attendanceOvertimes.employeeApproves.scopes', // Approve scopes
            'attendance.attendanceOvertimes.employee.title',

            // Entity Tags
            'entityTags',

            // Shift
            'shift',

            // Employee Relationships
            'employee.title:id,name'.$lang.',color',
            'employee:id,employee_number,is_trackable,status,first_name_ar,first_name_en,second_name_ar,second_name_en,title_id,employees.branch_id',
            'employee.branch:id,name'.$lang,
            'employee.employeeInfo:id,employee_id,join_date,termination_date',
            'employee.profilePicture',

            // Timecard Type
            'timecardType:id,name',
        ]);

        $obj = $this->getModel
            ->with($relationships)
            ->when(isset($request['start_date']), function ($q) use ($request) {
                $q->whereDate('from', '>=', $request['start_date'])
                    ->whereDate('from', '<=', $request['end_date']);  // end_date required with start_date
            })
            ->when(isset($request['employee_id']), function ($q) use ($request) {
                $q->where('employee_id', $request['employee_id']);
            })
            ->when(isset($request['branch_ids']) && ! isset($request['monthly_view_request']), function ($q) use ($request) {
                $q->where(function ($q) use ($request) {
                    $q->whereIn('timecards.branch_id', $request['branch_ids'])
                        ->orWhereIn('timecards.required_ci_branch_id', $request['branch_ids'])
                        ->orWhereIn('timecards.required_co_branch_id', $request['branch_ids']);
                });
            })
            ->when(! isset($request['employee_id']), function ($query) use ($scopeBranchIds, $request) {
                $this->appendScopeQuery($query, $request);
                $query->where(function ($q) use ($scopeBranchIds) {
                    $q->where(function ($attendanceQuery) use ($scopeBranchIds) {
                        $attendanceQuery->WhereDoesntHave('attendance')
                            ->whereIn('timecards.branch_id', $scopeBranchIds);
                    })
                        ->orWhereHas('attendance', function ($attendanceQuery) use ($scopeBranchIds) {
                            $attendanceQuery->whereIn('attendances.branch_id', $scopeBranchIds);
                            $attendanceQuery->orWhereHas('clockOut', function ($clockOutQuery) use ($scopeBranchIds) {
                                $clockOutQuery->whereIn('cicos.branch_id', $scopeBranchIds);
                            });
                        });
                })
                    ->when(isset($request['search_value']), function ($q) use ($request) {
                        $q->whereHas('employee', function ($q) use ($request) {
                            $q->where('name_ar', 'LIKE', '%'.$request['search_value'].'%')
                                ->orWhere('name_en', 'LIKE', '%'.$request['search_value'].'%')
                                ->orWhere('employee_number', 'LIKE', '%'.$request['search_value'].'%');
                        });
                    })
                    ->when(isset($request['status']), function ($q) use ($request) {
                        $q->whereHas('employee', function ($query) use ($request) {
                            $query->where('status', $request['status']);
                        });
                    });
            })
            ->when(isset($request['filter_keys']), function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $filterKeys = $request['filter_keys'];
                    // If 'late' is selected, add 'excused' to the filter keys
                    if (in_array('late', $filterKeys)) {
                        $filterKeys = array_merge($filterKeys, ['excused']);
                    }
                    
                    $query->whereHas('entityTags', function ($query) use ($filterKeys) {
                        $query->whereIn('tag', $filterKeys);
                    })->orWhereHas('attendance.entityTags', function ($query) use ($filterKeys) {
                        $query->whereIn('tag', $filterKeys);
                    });
                });
            })
            ->when(isset($request['with_area']), function ($q) use ($lang) {
                $q->with('employee.branch.area:id,name'.$lang);
            })
            ->when(! isset($request['approved_only']), function ($q) use ($isEarlyClockoutDeductionEnabled) {
                $q
                    ->with('attendance.attendanceDeductions', function ($query) {
                        $this->appendWithEmployeeApproves($query);
                        $query->where('deduction_value', '>', 0);
                    })
                    ->with('attendance.attendanceOvertimes', function ($query) {
                        $this->appendWithEmployeeApproves($query);
                    });
                    if($isEarlyClockoutDeductionEnabled){
                        $q->with('attendance.attendanceEarlyClockOutDeduction', function ($query) {
                            $this->appendWithEmployeeApproves($query);
                            $query->where('deduction_value', '>', 0);
                        });
                    }   
            })
            ->when(isset($request['approved_only']), function ($q) use ($isEarlyClockoutDeductionEnabled) {
                $q->with('attendance.attendanceDeductions', function ($query) {
                    $this->appendWithEmployeeApproves($query);
                    $query->where('status', 'applied')
                        ->where('deduction_value', '>', 0);
                })
                    ->with('attendance.attendanceOvertimes', function ($query) {
                        $this->appendWithEmployeeApproves($query);
                        $query->where('status', 'approved');
                    });
                    if($isEarlyClockoutDeductionEnabled){
                        $q->with('attendance.attendanceEarlyClockOutDeduction', function ($query) {
                            $this->appendWithEmployeeApproves($query);
                            $query->where('status', 'applied')
                                ->where('deduction_value', '>', 0);
                        });
                    }

            })
            ->when(isset($request['with_pending_overtime_only']), function ($q) {
                $q->whereHas('attendance.attendanceOvertimes', function ($query) {
                    $query->where('attendance_overtimes.status', 'pending');
                });
            })
            ->when(isset($request['with_pending_waive_deduction_only']), function ($q) {
                $q->whereHas('attendance.attendanceDeductions', function ($query) {
                    $this->appendPendingWaiveDeductionQuery($query);
                    $query->where('deduction_value', '>', 0);
                });
            });

            if($isEarlyClockoutDeductionEnabled){
                $obj->when(isset($request['with_pending_waive_early_clockout_deduction_only']), function ($q) {
                    $q->whereHas('attendance.attendanceEarlyClockOutDeduction', function ($query) {
                        $this->appendPendingWaiveDeductionQuery($query);
                        $query->where('deduction_value', '>', 0);
                    });
                });
            }
            

//            ->explain()->dd();
            $obj->orderBy($request['order_by'], $request['order_by_type'])
            ->orderBy('timecards.id', 'asc');

        return $this->getResult($obj, $request);
    }

    public function getFilterPaginated($obj, $pageSize)
    {
        return $obj->paginate($pageSize);
    }

    public function getResult($obj, $request)
    {
        if ($request['page_size'] == 0) {
            return $obj->get();
        } else {
            return $this->getFilterPaginated($obj, $request['page_size']);
        }
    }
    //    public function getResult($obj, $request)
    //    {
    //        if ($request['page_size'] == 0) {
    //            return $obj->get();
    //        } else {
    //            return $obj->orderBy('id')->cursorPaginate($request['page_size']);
    //        }
    //    }

    public function getTimecardsInBranchWithoutCustom($branchId, $startDate, $endDate)
    {
        $query = $this->getModel
            ->with('employee:id,title_id,company_id',
                'employee.title:id,work_type_policy_id',
                'employee.title.workTypePolicy:id,work_days_type')
            ->whereHas('employee', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->whereDate('from', '>=', $startDate)
            ->whereDate('from', '<=', $endDate)
            ->whereNotNull('shift_id'); // dont get custom timecards
        $this->appendScopeQuery($query);

        return $query->get();
    }

    public function deleteOnDate($employeeId, $date)
    { // timestamps $from and $to
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->delete();
    }

    public function getWithAttendance($id)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        // Check if the feature flag is enabled
        $unleash = app(Unleash::class);
        $isEarlyClockoutDeductionEnabled = $unleash->isEarlyClockoutDeductionEnabled();
        $isMissingClockoutDeductionEnabled = $unleash->isMissingClockoutDeductionEnabled();

        $query = $this->getModel->with(
            'attendance.clockIn.branch:id,name'.$lang, 'attendance.clockOut.branch:id,name'.$lang,
            'attendance.entityTags', 'entityTags', 'shift', 'employee.title:id,name'.$lang.',color', 'employee:id,employee_number,name_ar,name_en,first_name_ar,first_name_en,second_name_ar,second_name_en,title_id,branch_id',
            'employee.branch:id,name'.$lang.'', 'employee.profilePicture', 'timecardType:id,name', 'childTimecard:id,from,to,created_at,parent_timecard_id,name,timecard_type_id,employee_id', 'childTimecard.timecardType:id,name')
            ->with('attendance.attendanceDeductions', function ($query) {
                $query
                    ->with('employeeApproves.users.employee', function ($q) {
                        $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                            ->with('branches', function ($q) {
                                $q->select('branch_employee.id', 'branch_id');
                            });
                    });
            })
            ->with('attendance.attendanceOvertimes', function ($query) {
                $query
                    ->with('employeeApproves.users.employee', function ($q) {
                        $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                            ->with('branches', function ($q) {
                                $q->select('branch_employee.id', 'branch_id');
                            });
                    });
            });

        // Add missing clockout deductions if feature flag is enabled
        if ($isEarlyClockoutDeductionEnabled) {
            $query->with('attendance.attendanceEarlyClockOutDeduction');
        }
        if ($isMissingClockoutDeductionEnabled) {
            $query->with('attendance.missingClockoutDeductions');
        }

        return $query->find($id);

    }

    public function getTimecardsThatHasAttendanceWithSpecificTag($tag)
    {
        return $this->getModel->whereHas('attendance')
            ->whereHas('entityTags', function ($q) use ($tag) {
                $q->where('tag', $tag);
            })->get();
    }

    public function getByIds($ids)
    {
        return $this->getModel->whereIn('id', $ids)->whereHas('employee', function ($query) {
            $query->whereNull('deleted_at');
        })->get();
    }

    public function getTimecardsCountGrouped($fromDate, $toDate, $employeeIds = null)
    {
        return $this->getModel
            ->join('attendances', function ($q) {
                $q->on('timecards.id', '=', 'attendances.slotable_id')
                    ->where('attendances.slotable_type', '=', 'time_card');
            })
            ->join('cicos', 'attendances.co_id', '=', 'cicos.id')
            ->join('entity_tags', function ($q) {
                $q->on('attendances.id', '=', 'entity_tags.entity_id')
                    ->where('entity_tags.entity_type', '=', 'attendance')
                    ->where('entity_tags.tag', '!=', 'considered_absent');
            })
            ->when(isset($employeeIds), function ($q) use ($employeeIds) {
                $q->whereIn('timecards.employee_id', $employeeIds);
            })
            ->whereDate('from', '>=', $fromDate)
            ->whereDate('from', '<=', $toDate)
            ->select([
                'timecards.employee_id',
                'timecards.timecard_type_id',
                'cicos.branch_id',
                DB::raw('COUNT(DISTINCT DATE(timecards.from)) as timecards_count'),
            ])
            ->groupBy(
                'timecards.employee_id',
                'timecards.timecard_type_id',
                'cicos.branch_id')
            ->get();
    }

    // TODO to be deleted
    public function checkOverlappingTimeCardsWithBranch(int $employeeId, string $fromDate, string $toDate, int $branchId): bool
    {
        return $this->getModel->where('employee_id', $employeeId)
            ->whereDoesntHave('entityTags', function ($q) {
                $q->where('tag', '=', 'absent');
            })
            ->where('to', '>=', $fromDate)
            ->where('from', '<=', $toDate)
            ->where('branch_id', $branchId)
            ->exists();
    }

    public function getEmployeesAbsenceDays($fromDate, $toDate): mixed
    {
        return $this->getModel
            ->where(function ($q) {
                $q->whereHas('entityTags', function ($q) {
                    $q->whereIn('tag', [PayrollUtil::ABSENCE_TYPES['ABSENT'], PayrollUtil::ABSENCE_TYPES['ABSENT_WITHOUT_PERMISSION']]);
                })
                    ->orWhereHas('attendance.entityTags', function ($q) {
                        $q->where('tag', PayrollUtil::ABSENCE_TYPES['CONSIDERED_ABSENT']);
                    });
            })
            ->whereHas('employee', function ($q) {
                $q->where('company_id', config('globals.company')->id);
            })
            ->whereNull('timecards.deleted_at')
            ->whereDate('from', '>=', $fromDate)
            ->whereDate('from', '<=', $toDate)
            ->orderBy('from', 'asc')
            ->with('entityTags')
            ->get();
    }

    public function getTimecardIdsStartingFromDate($employeeId, $date)
    {
        return $this->getModel
            ->select(['id'])
            ->whereDoesntHave('attendance')
            ->where('employee_id', $employeeId)
            ->where('to', '>=', $date)->get();
    }

    public function getEmployeesAbsenceCount($fromDate, $toDate, $withoutTerminatedEmployees = false, $employeeIds = null): mixed
    {
        return $this->getModel
            ->whereHas('entityTags', function ($q) {
                $q->whereIn('tag', [PayrollUtil::ABSENCE_TYPES['ABSENT'], PayrollUtil::ABSENCE_TYPES['ABSENT_WITHOUT_PERMISSION']]);
            })
            ->whereHas('employee', function ($q) use ($withoutTerminatedEmployees, $employeeIds) {
                $q->where('company_id', config('globals.company')->id)
                    ->when($withoutTerminatedEmployees, function ($q) {
                        $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED']);
                    })
                    ->when(isset($employeeIds), function ($q) use ($employeeIds) {
                        $q->whereIn('id', $employeeIds);
                    });
            })
            ->whereNull('timecards.deleted_at')
            ->whereDate('from', '>=', $fromDate)
            ->whereDate('from', '<=', $toDate)
            ->select([
                'timecards.employee_id',
                DB::raw('COUNT(DISTINCT DATE(timecards.from)) as absence_days'),
            ])
            ->groupBy('employee_id')
            ->get();
    }

    public function countEmployeeTimecardsWithAttendanceFromDate($employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->where('from', '>=', $date)
            ->whereHas('attendance')
            ->count();
    }

    public function deleteEmployeeTimecardsWithoutAttendanceFromDate($employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->where('from', '>=', $date)
            ->whereDoesntHave('attendance')
            ->delete();
    }

    public function getTimecardIdsWithoutAttendanceOnDate(int $employeeId, string $from, $exceptId = null)
    {
        return $this->getModel
            ->when(isset($exceptId), function ($q) use ($exceptId) {
                $q->where('id', '!=', $exceptId);
            })
            ->where('employee_id', $employeeId)
            ->whereDate('from', $from)
            ->whereDoesntHave('attendance')
            ->whereHas('timecardType', function ($q) {
                $q->whereNotIn('name', [MissionsEnum::HALF_DAY_BEFORE_WORK->value, MissionsEnum::HALF_DAY_AFTER_WORK->value]);
            })
            ->pluck('id')
            ->unique()
            ->toArray();

    }

    public function getOverlappingTimecardWithAttendance($employeeId, $dateTime, $slotId = null)
    {
        return $this->getModel
            ->where('id', '!=', $slotId)
            ->where('employee_id', $employeeId)
            ->where('from', '<=', $dateTime)
            ->where('to', '>=', $dateTime)
            ->whereHas('attendance')
            ->first();
    }

    public function getOverlappingTimecardWithClockInOut($employeeId, $clockIn, $clockOut, $slotId = null)
    {
        return $this->getModel
            ->where('id', '!=', $slotId)
            ->where('employee_id', $employeeId)
            ->where(function ($q) use ($clockIn, $clockOut) {
                $q->where(function ($q) use ($clockIn) {
                    $q->where('from', '<=', $clockIn)
                        ->where('to', '>=', $clockIn);
                })
                    ->orWhere(function ($q) use ($clockOut) {
                        $q->where('from', '<=', $clockOut)
                            ->where('to', '>=', $clockOut);
                    })
                    ->orWhere(function ($q) use ($clockIn, $clockOut) {
                        $q->where('from', '>=', $clockIn)
                            ->where('to', '<=', $clockOut);
                    });

            })
            ->whereHas('attendance')
            ->first();
    }

    public function timecardsOnDateForEmployee(int $employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->get();
    }

    public function countOfTimecardByTagsInDate(string $date, array $tags)
    {
        $query = $this->getModel
            ->whereDate('from', $date)
            ->whereHas('entityTags', function ($query) use ($tags) {
                $query->whereIn('tag', $tags);
            });
        $this->appendScopeQuery($query);

        return $query->count();
    }

    public function countOfAbsentTimeCardsByTagInMonthAndYear(string $start, string $end)
    {
        $query = $this->getModel
            ->whereDate('from', '>=', $start)
            ->whereDate('from', '<=', $end)
            ->whereHas('entityTags', function ($query) {
                $query->whereIn('tag', [PayrollUtil::ABSENCE_TYPES['ABSENT'], PayrollUtil::ABSENCE_TYPES['ABSENT_WITHOUT_PERMISSION']]);
            });
        $this->appendScopeQuery($query);

        return $query->count();
    }

    public function countOfLateIncidenceInMonthAndYear(string $start, string $end, ?int $employeeId = null)
    {
        $query = $this->getModel
            ->whereDate('from', '>=', $start)
            ->whereDate('from', '<=', $end)
            ->when(isset($employeeId) && $employeeId, function ($query) use ($employeeId) {
                $query->where('employee_id', $employeeId);
            })
            ->whereHas('attendance', function ($query) {
                $query->whereHas('entityTags', function ($query) {
                    $query->where('tag', 'late')->where('entity_type', 'attendance');
                });

            });

        $this->appendScopeQuery($query);

        return $query->count();

    }

    public function getEmployeesPresentCount($fromDate, $toDate, $employeeIds)
    {
        return $this->getModel
            ->whereDate('from', '>=', $fromDate)
            ->whereDate('from', '<=', $toDate)
            ->whereHas('attendance')
            ->whereHas('employee', function ($q) use ($employeeIds) {
                $q->when(isset($employeeIds), function ($q) use ($employeeIds) {
                    $q->whereIn('id', $employeeIds);
                });
            })
            ->select('employee_id', DB::raw('COUNT(DISTINCT DATE(timecards.from)) as present_count'))
            ->groupBy('employee_id')
            ->get();
    }

    public function getTimecardsWithoutAttendanceOnDate($employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->doesntHave('attendance')
            ->whereDate('from', $date)
            ->get();
    }

    public function timecardHasAttendanceOnDateForEmployee(int $employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereHas('attendance')
            ->whereDate('from', $date)
            ->first();
    }

    public function deleteTimecardsAfterDate($employeeId, $date): void
    {
        $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', '>', $date)
            ->delete();
    }

    public function getTimecardIdsAfterDate($employeeId, $date)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereDate('from', '>', $date)
            ->get()
            ->pluck('id')
            ->toArray();
    }

    public function getLatestTimecardForEmployee(int $employeeId)
    {
        return $this->getModel
            ->where('employee_id', $employeeId)
            ->whereHas('attendance')
            ->orderBy('from', 'desc')
            ->first();
    }

    public function getLateIncidentsForEmployees(string $start, string $end, $employeeIds)
    {
        $query = $this->getModel
            ->whereDate('from', '>=', $start)
            ->whereDate('from', '<=', $end)
            ->whereIn('employee_id', $employeeIds)
            ->whereHas('attendance', function ($query) {
                $query->whereHas('entityTags', function ($query) {
                    $query->where('tag', 'late')->where('entity_type', 'attendance');
                });
            });

        $this->appendScopeQuery($query);

        return $query->get(['employee_id', 'id']); // Fetch only necessary fields
    }
}
