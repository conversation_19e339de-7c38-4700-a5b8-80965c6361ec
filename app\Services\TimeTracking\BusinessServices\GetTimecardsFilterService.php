<?php

namespace App\Services\TimeTracking\BusinessServices;

use App\FeatureToggles\Unleash;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\EarlyClockoutDeductionsRepository;
use App\Repositories\V1\Attendance\MissingClockoutDeductionRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Services\IBusinessService;
use App\Traits\DataPreparation;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use Carbon\Carbon;
use stdClass;

class GetTimecardsFilterService implements IBusinessService
{
    use DataPreparation, GetLastDraftedPayRollOrCreate, PayrollHelper;

    private Repository $timecardRepository;

    private Repository $employeeRepository;

    private Repository $departmentRepository;

    private Repository $titlesRepository;

    private Repository $employeeLeaveRequestRepository;

    private Repository $employeeLeaveBalanceRepository;

    public function __construct(private PublicHolidaysRepository $publicHolidaysRepository,
    private PayrollsRepository       $payrollsRepository,
    private SystemSettingRepository  $systemSettingRepository,
    )
    {
        $this->titlesRepository = Repository::getRepository('Title');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->departmentRepository = Repository::getRepository('Department');
        $this->employeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->timecardRepository = Repository::getRepository('Timecard');
        
    }
    
    public function isValid(array $request, stdClass &$output): bool
    {
        return true;
    }
    
    public function perform(array $request, stdClass &$output): void
    {
        if (isset($request['with_pending_overtime_only']) || isset($request['with_pending_waive_deduction_only'])) {
            unset($request['approved_only']);
        }

        // Check if the feature flag is enabled
        $unleash = app(Unleash::class);
        $isEarlyClockoutDeductionEnabled = $unleash->isEarlyClockoutDeductionEnabled();
        $isMissingClockoutDeductionEnabled = $unleash->isMissingClockoutDeductionEnabled();
        
        $timecards = $this->timecardRepository->getWithAttendanceAndCico($request);
        
        // Process attendance deductions (late deductions)
        foreach ($timecards as $timecard) {
            if (isset($timecard?->attendance?->attendanceDeductions) && $timecard?->attendance?->attendanceDeductions->count() > 0) {
                $deduction = $timecard?->attendance?->attendanceDeductions->first();
                $employeeRequest = $timecard->attendance->attendanceDeductions->first()->employeeRequests->first();

                $basicDeductionValue = $deduction->deduction_value;
                $updatedDeductionValue = $deduction->updated_value ?? null;
                $timecardDate = $timecard->from;
                $employee = $timecard->employee;
                // $clockInTime  = Cico::find($timecard?->attendance?->ci_id)?->date ?? null;
                // $clockOutTime = Cico::find($timecard?->attendance?->co_id)?->date ?? null;
                $clockInTime = $timecard?->attendance?->clockIn?->date ?? null;
                $clockOutTime = $timecard?->attendance?->clockOut?->date ?? null;

                if (!$timecardDate) {
                    continue;
                }
                $payrollPeriods = $this->getPayrollPeriodFromDate($timecardDate);
                if (!$payrollPeriods) {
                    continue;
                }

                $numberOfLateIncidents = $this->timecardRepository->countOfLateIncidenceInMonthAndYear($payrollPeriods['start'], $payrollPeriods['end'], $employee->id);

                if (!$clockInTime || !$clockOutTime) {
                    $workedHours = null;
                    $lateDuration = null;
                } else {
                    $workedHours = Carbon::parse($clockOutTime)->diffInMinutes(Carbon::parse($clockInTime));
                    $lateDuration = Carbon::parse($clockInTime)->diffInMinutes(Carbon::parse($timecardDate));
                }

                $deductionEditRequest = null;

                if (isset($employeeRequest) && $employeeRequest && !is_null($employeeRequest->status)) {
                    $deductionEditRequest = [
                        //employee request is initalized with pending then after workflow it will be updated to the enitity status waivded or applied
                        'status' => $employeeRequest?->status == 'pending' ? 'pending' : 'completed',
                        'deduction_value' => $updatedDeductionValue ?? $basicDeductionValue,
                        'requester_name' => $employeeRequest?->requestedBy?->name ?? '',
                        'reason' => $employeeRequest?->comment ?? '',
                    ];

                    $isDeductionRequestRejected = $deduction->workflowApprovalCycles->where('status', '=', 'rejected')->count() > 0;

                    if ($isDeductionRequestRejected) {
                        $deductionEditRequest = array_merge($deductionEditRequest, ['deduction_value' => $basicDeductionValue]);
                    }

                    $requestHaveApprovedOrOperator = $deduction->workflowApprovalCycles->where('status', '=', 'approved')
                            ->where('operator', 'or')
                            ->count() > 0;

                    if ($requestHaveApprovedOrOperator) {
                        $deductionEditRequest = array_merge($deductionEditRequest, ['deduction_value' => $updatedDeductionValue]);
                    }

                }
                $timecard->deduction_edit_request = $deductionEditRequest;
                $timecard->number_of_late_incidents = $numberOfLateIncidents;
                $timecard->late_duration = $lateDuration;
                $timecard->worked_hours = $workedHours;
            }

            $timecardDate = $timecard->from;
            $employee = $timecard->employee;
            if (!$timecardDate) {
                continue;
            }

            $payrollPeriods = $this->getPayrollPeriodFromDate($timecardDate);
            if (!$payrollPeriods) {
                continue;
            }
            // Process missing clockout deductions if feature flag is enabled
            if ($isMissingClockoutDeductionEnabled) {
                if(isset($timecard?->attendance?->missingClockoutDeductions) &&
                $timecard?->attendance?->missingClockoutDeductions->count() > 0){
                    $missingClockoutDeduction = $timecard?->attendance?->missingClockoutDeductions->first();


                    // Get the repository for missing clockout deductions
                    $missingClockoutDeductionRepository = app(MissingClockoutDeductionRepository::class);

                    // Count missing clockout incidents for the employee in the payroll period
                    $numberOfMissingClockoutIncidents = $missingClockoutDeductionRepository->countMissingClockoutsForEmployee(
                        $employee->id,
                        $payrollPeriods['start'],
                        $payrollPeriods['end']
                    );
                    $timecard->number_of_missing_clockout_incidents = $numberOfMissingClockoutIncidents ?? 0;
                }
            }
            if($isEarlyClockoutDeductionEnabled){
                if(isset($timecard?->attendance?->attendanceEarlyClockOutDeduction)){
                    $earlyClockoutDeduction = $timecard?->attendance?->attendanceEarlyClockOutDeduction;
                    $employeeRequest = $earlyClockoutDeduction->employeeRequest;
                    // Get the repository for early clockout deductions
                    $earlyClockoutDeductionRepository = app(EarlyClockoutDeductionsRepository::class);
                    // Count early clockout incidents for the employee in the payroll period
                    $numberOfEarlyClockoutIncidents = $earlyClockoutDeductionRepository->countDeductionsForEmployee(
                        $employee->id,
                        $payrollPeriods['start'],
                        $payrollPeriods['end']
                    );
                    $deductionEditRequest = null;
    
                    if (isset($employeeRequest) && $employeeRequest && !is_null($employeeRequest->status)) {
                        $deductionEditRequest = [
                            //employee request is initalized with pending then after workflow it will be updated to the enitity status waivded or applied
                            'status' => $employeeRequest?->status == 'pending' ? 'pending' : 'completed',
                            'deduction_value' => $earlyClockoutDeduction->updated_value ??$earlyClockoutDeduction->deduction_value,
                            'requester_name' => $employeeRequest?->requestedBy?->name ?? '',
                            'reason' => $employeeRequest?->comment ?? '',
                        ];
    
                        $isDeductionRequestRejected = $earlyClockoutDeduction->workflowApprovalCycles->where('status', '=', 'rejected')->count() > 0;
    
                        if ($isDeductionRequestRejected) {
                            $deductionEditRequest = array_merge($deductionEditRequest, ['deduction_value' => $earlyClockoutDeduction->deduction_value]);
                        }
    
                        $requestHaveApprovedOrOperator = $earlyClockoutDeduction->workflowApprovalCycles->where('status', '=', 'approved')
                                ->where('operator', 'or')
                                ->count() > 0;
    
                        if ($requestHaveApprovedOrOperator) {
                            $deductionEditRequest = array_merge($deductionEditRequest, ['deduction_value' => $earlyClockoutDeduction->updated_value]);
                        }
                }

                $earlyClockoutDuration = Carbon::parse($timecard?->attendance?->clockOut?->date ?? null)->diffInMinutes(Carbon::parse($timecard->to));
                // Add missing clockout incidents count to the timecard
                $timecard->number_of_early_clockout_incidents = $numberOfEarlyClockoutIncidents ?? 0;
                $timecard->early_clockout_duration = $earlyClockoutDuration;
                $timecard->early_clockout_edit_request = $deductionEditRequest;
            }
        }
        foreach ($timecards as $timecard) {
            if (isset($timecard?->attendance?->attendanceOvertimes) && $timecard?->attendance?->attendanceOvertimes->count() > 0) {
                $overtime = $timecard?->attendance?->attendanceOvertimes->first();
                $employeeRequest = $timecard->attendance->attendanceOvertimes->first()->employeeRequests->first();

                $basicOvertimeValue = $overtime->overtime_minutes;
                $updatedOvertimeValue = $overtime->updated_value;

                $overtimeEditRequest = [
                    //employee request is initalized with pending then after workflow it will be updated to the enitity status waivded or applied
                    'status' => $employeeRequest?->status == 'pending' ? 'pending' : 'completed',
                    'overtime_minutes' => $updatedOvertimeValue ?? $basicOvertimeValue,
                    'requester_name' => $employeeRequest?->requestedBy?->name ?? '',
                    'reason' => $employeeRequest?->comment ?? '',
                ];

                $timecard->overtime_edit_request = $overtimeEditRequest;

            }
        }
        $timecardDates = collect($timecards)->pluck('from')->map(fn($date) => Carbon::parse($date)->toDateString());

        $publicHolidays = $this->publicHolidaysRepository->getPublicHolidaysForDates($timecardDates);
        
        $holidayRanges = $publicHolidays->map(function ($holiday) {
            return [
                'start' => Carbon::parse($holiday->start),
                'end' => Carbon::parse($holiday->end),
            ];
        });

        foreach ($timecards as $timecard) {
            $date = Carbon::parse($timecard->from)->toDateString();

            // Check if the date falls within any public holiday range
            $timecard->is_public_holiday = $holidayRanges->contains(function ($range) use ($date) {
                return Carbon::parse($date)->between($range['start'], $range['end']);
            });
        }
    }
    $output->verified_attendance = $timecards;
}
    
    public function getVerifiedAttendance(array $request)
    {
        return $this->timecardRepository->getWithAttendanceAndCico($request);
    }

    public function getBranchIds($branches)
    {
        return array_map(function ($branch) {
            return $branch['id'];
        }, $branches->toArray());
    }

    public function getTitleIds($titles)
    {
        return array_map(function ($title) {
            return $title['id'];
        }, $titles->toArray());
    }

    public function getDepartmentIds($departments)
    {
        return array_map(function ($department) {
            return $department['id'];
        }, $departments->toArray());
    }
}
