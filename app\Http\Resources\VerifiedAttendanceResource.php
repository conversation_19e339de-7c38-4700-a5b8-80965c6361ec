<?php

namespace App\Http\Resources;

use App\Enums\EntityTags\AttendanceTags;
use App\Enums\EntityTags\ClockInTags;
use App\Enums\EntityTags\ClockOutTags;
use App\Enums\Missions\MissionsEnum;
use App\FeatureToggles\Unleash;
use App\Http\Resources\V1\WorkFlows\ApprovalCycleCollection;
use App\Traits\QueriesHelper;
use App\Traits\RequestsHelper;

class VerifiedAttendanceResource extends BaseResource
{
    use QueriesHelper, RequestsHelper;

    protected $authEmployee;

    public function __construct($resource, $authEmployee)
    {
        parent::__construct($resource);
        $this->authEmployee = $authEmployee;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $tags = $this->entityTags?->merge($this->attendance?->entityTags ?? []) ?? [];
        $this->employee->profile_image = isset($this->employee->profilePicture) ? $this->employee->profilePicture?->attachment_url : null;
        unset($this->employee->profilePicture);

        return [
            'id' => $this->id,
            'timecard_name' => $this->name ?? $this->shift?->name ?? $this->timecardType?->name ?? null,
            'created_by_id' => $this->created_by_id ?? null,
            'shift_id' => $this->shift_id,
            'date' => $this->from,
            'is_public_holiday' => $this->is_public_holiday ?? false,
            'is_half_day_mission' => in_array($this->timecardType?->name, [MissionsEnum::HALF_DAY_BEFORE_WORK->value, MissionsEnum::HALF_DAY_AFTER_WORK->value]) ?? false,
            'employee' => $this->employee,
            'employee_id' => $this->employee->id,
            'attendance_id' => $this->attendance->id ?? null,
            'clock_in_date' => $this->attendance->clockIn->date ?? null,
            'clock_out_date' => $this->attendance->clockOut->date ?? null,
            'ci_by_tablet' => (bool) ($this->attendance->clockIn->by_tablet ?? false),
            'co_by_tablet' => (bool) ($this->attendance->clockOut->by_tablet ?? false),
            'clock_in' => $this->attendance->clockIn ?? null,
            'clock_out' => $this->attendance->clockOut ?? null,
            'actual_start' => $this->from,
            'actual_end' => $this->to,
            'required_ci_branch_id' => $this->required_ci_branch_id,
            'required_co_branch_id' => $this->required_co_branch_id,
            'required_ci_lat' => $this->required_ci_lat,
            'required_ci_long' => $this->required_ci_long,
            'required_co_lat' => $this->required_co_lat,
            'required_co_long' => $this->required_co_long,
            'co_branch' => isset($this->attendance) && isset($this->attendance->clockOut) && ! is_null($this->attendance->clockOut->branch) ?
                $this->attendance->clockOut->branch : $this->attendance?->clockOut?->branchWithTrashed,

            'ci_branch' => isset($this->attendance) && isset($this->attendance->clockIn) && ! is_null($this->attendance->clockIn->branch) ?
                $this->attendance->clockIn->branch : $this->attendance?->clockIn?->branchWithTrashed,

            'co_in_his_main_branch' => isset($this->attendance->clockOut) ? $this->employee->branch_id === $this->attendance->clockOut->branch_id : false,
            'co_branch_source' => $this->getCoBranchSource(),
            'overtime' => (! is_null($this->attendance) && ! is_null($this->attendance->attendanceOvertimes) && count($this->attendance->attendanceOvertimes)) ?
                [
                    'id' => $this->attendance->attendanceOvertimes[0]->id,
                    'status' => $this->attendance->attendanceOvertimes[0]->status,
                    'base_overtime_value' => $this->attendance->attendanceOvertimes[0]->overtime_value,
                    'base_overtime_minutes' => $this->attendance->attendanceOvertimes[0]->overtime_minutes,
                    'updated_overtime_value' => $this->attendance->attendanceOvertimes[0]->updated_value,
                    'note' => $this->attendance->attendanceOvertimes[0]->note,
                    'approval_cycle' => isset($this->attendance->attendanceOvertimes) &&
                    isset($this->attendance->attendanceOvertimes[0]->employeeApproves) ?
                        new ApprovalCycleCollection($this->getApprovalCycle($this->attendance->attendanceOvertimes[0], $this->attendance->attendanceOvertimes[0]->employee))
                        : null,

                    'edit_request' => $this->whenAttributeExists('overtime_edit_request'),
                    'actionable' => isset($this->attendance->attendanceOvertimes) &&
                        isset($this->attendance->attendanceOvertimes[0]->employeeApproves)
                        && $this->attendance->attendanceOvertimes[0]->status == 'pending'
                        && $this->isActionable($this->attendance->attendanceOvertimes[0]->employeeApproves, $this->attendance->attendanceOvertimes[0]?->employeeRequest?->status),
                    'can_cancel' => isset($this->attendance->attendanceOvertimes) &&
                        isset($this->attendance->attendanceOvertimes[0]?->employeeApproves) && $this->canCancel($this->attendance->attendanceOvertimes[0]?->employeeApproves, $this->attendance->attendanceOvertimes[0]?->employeeRequest?->status),
                ] : null,
            'deduction' => $this->getDeductionData(),
            'tags' => EntityTagResource::collection($tags),
            'history' => $this->whenAttributeExists('history'),
            'action' => $this->action(),
            'off_shift_deleted_TCs' => $this->whenAttributeExists('off_shift_deleted_TCs'),
            'new_tags' => $this->getNewTags(),

        ];
    }

    public function getNewTags(): array
    {
        $clockInTags = [];
        $clockOutTags = [];
        $specialTags = [];
        $entityTags = $this->attendance->entityTags ?? $this->entityTags;

        if (! empty($entityTags)) {

            foreach ($entityTags as $entityTag) {
                $tag = $entityTag->tag;

                if (in_array($tag, ClockInTags::values(), true)) {
                    $clockInTags[] = $entityTag;
                } elseif (in_array($tag, ClockOutTags::values(), true)) {
                    $clockOutTags[] = $entityTag;
                } elseif (in_array($tag, AttendanceTags::values(), true)) {
                    $specialTags[] = $entityTag;
                }
            }
        }

        return [
            'clock_in_tags' => new EntityTagCollection($clockInTags),
            'clock_out_tags' => new EntityTagCollection($clockOutTags),
            'special_tags' => new EntityTagCollection($specialTags),
        ];
    }

    public function getManagerBranch(): ?int
    {
        if (! $this->authEmployee) {
            return null;
        }

        if (! $this->authEmployee instanceof \App\Models\Employee) {
            return null;
        }

        return $this->authEmployee?->branch_id ?? null;
    }

    public function getManagerBranches(): array
    {
        if (! $this->authEmployee) {
            return [];
        }

        if (! $this->authEmployee instanceof \App\Models\Employee) {
            return [];
        }

        return $this->authEmployee?->branches->pluck('id')->toArray() ?? [];
    }

    public function getCoBranchSource(): ?string
    {
        if (isset($this->attendance->clockOut) && $this->employee && isset($this->attendance->clockOut->branch)) {
            if (($this->attendance->clockOut->branch_id === $this->getManagerBranch() || in_array($this->attendance->clockOut->branch_id, $this->getManagerBranches())) && $this->employee->branch_id !== $this->attendance->clockOut->branch_id) {
                return 'from';
            }

            if (($this->attendance->clockOut->branch_id !== $this->getManagerBranch() || ! in_array($this->attendance->clockOut->branch_id, $this->getManagerBranches())) && $this->employee->branch_id !== $this->attendance->clockOut->branch_id) {
                return 'at';
            }
        }

        return null;
    }

    public function action(): bool
    {
        // Logic is deprecated, frontend should always allow the action
        //        if (isset($this->attendance->clockIn)) {
        //            if (($this->attendance->clockIn->branch_id == $this->getManagerBranch() || in_array($this->attendance->clockIn->branch_id, $this->getManagerBranches()))) {
        //                return true;
        //            } else {
        //                return false;
        //            }
        //        }

        return true;
    }

    protected function getDeductionData()
    {
        $unleash = app(Unleash::class);
        $isEarlyClockoutDeductionEnabled = $unleash->isEarlyClockoutDeductionEnabled();
        $isMissingClockoutDeductionEnabled = $unleash->isMissingClockoutDeductionEnabled();
        if ($this->attendance === null) {
            return null;
        }

        $hasAttendanceDeductions = $this->attendance->attendanceDeductions != null &&
                                  count($this->attendance->attendanceDeductions) > 0;

        $hasMissingClockoutDeductions = $isMissingClockoutDeductionEnabled &&
                                       $this->attendance->missingClockoutDeductions != null &&
                                       count($this->attendance->missingClockoutDeductions) > 0;

        $hasEarlyClockOutDeductions = $isEarlyClockoutDeductionEnabled &&
                                      $this->attendance->attendanceEarlyClockOutDeduction != null;

        if (! $hasAttendanceDeductions && ! $hasMissingClockoutDeductions && ! $hasEarlyClockOutDeductions) {
            return null;
        }

        if (! $isEarlyClockoutDeductionEnabled && ! $isMissingClockoutDeductionEnabled) {
            if ($hasAttendanceDeductions) {
                return [
                    'id' => $this->attendance->attendanceDeductions[0]->id,
                    'status' => $this->attendance->attendanceDeductions[0]->status,
                    'base_deduction_value' => $this->attendance->attendanceDeductions[0]->deduction_value,
                    'updated_deduction_value' => $this->attendance->attendanceDeductions[0]->updated_value,
                    'approval_cycle' => isset($this->attendance->attendanceDeductions) &&
                    isset($this->attendance->attendanceDeductions[0]->employeeApproves) ?
                        new ApprovalCycleCollection($this->getApprovalCycle($this->attendance->attendanceDeductions[0], $this->attendance->attendanceDeductions[0]->employee))
                        : null,
                    'edit_request' => $this->whenAttributeExists('deduction_edit_request'),
                    'number_of_late_incidents' => $this->whenAttributeExists('number_of_late_incidents'),
                    'late_duration' => $this->whenAttributeExists('late_duration'),
                    'worked_hours' => $this->whenAttributeExists('worked_hours'),
                    'actionable' => isset($this->attendance->attendanceDeductions) &&
                        isset($this->attendance->attendanceDeductions[0]->employeeApproves)
                        && $this->attendance->attendanceDeductions[0]->status == 'applied'
                        && isset($this->attendance->attendanceDeductions[0]->employeeRequest)
                        && $this->isActionable($this->attendance->attendanceDeductions[0]->employeeApproves, $this->attendance->attendanceDeductions[0]->employeeRequest->status),
                    'can_cancel' => isset($this->attendance->attendanceDeductions)
                        && isset($this->attendance->attendanceDeductions[0]->employeeRequest) &&
                        isset($this->attendance->attendanceDeductions[0]->employeeApproves) && $this->canCancel($this->attendance->attendanceDeductions[0]->employeeApproves, $this->attendance->attendanceDeductions[0]->employeeRequest->status),
                ];
            }

            return null;
        }

        $deductions = [];

        if ($hasAttendanceDeductions) {
            $deductions[] = [
                'type' => 'late_deduction',
                'id' => $this->attendance->attendanceDeductions[0]->id,
                'status' => $this->attendance->attendanceDeductions[0]->status,
                'base_deduction_value' => $this->attendance->attendanceDeductions[0]->deduction_value,
                'updated_deduction_value' => $this->attendance->attendanceDeductions[0]->updated_value,
                'approval_cycle' => isset($this->attendance->attendanceDeductions) &&
                isset($this->attendance->attendanceDeductions[0]->employeeApproves) ?
                    new ApprovalCycleCollection($this->getApprovalCycle($this->attendance->attendanceDeductions[0], $this->attendance->attendanceDeductions[0]->employee))
                    : null,
                'edit_request' => $this->whenAttributeExists('deduction_edit_request'),
                'number_of_incidents' => $this->whenAttributeExists('number_of_late_incidents'),
                'duration' => $this->whenAttributeExists('late_duration'),
                'worked_hours' => $this->whenAttributeExists('worked_hours'),
                'actionable' => isset($this->attendance->attendanceDeductions) &&
                    isset($this->attendance->attendanceDeductions[0]->employeeApproves)
                    && $this->attendance->attendanceDeductions[0]->status == 'applied'
                    && $this->isActionable($this->attendance->attendanceDeductions[0]->employeeApproves, $this->attendance->attendanceDeductions[0]->employeeRequest?->status),
                'can_cancel' => isset($this->attendance->attendanceDeductions) &&
                    isset($this->attendance->attendanceDeductions[0]->employeeApproves) &&
                    $this->canCancel($this->attendance->attendanceDeductions[0]->employeeApproves, $this->attendance->attendanceDeductions[0]->employeeRequest?->status),
            ];
        }

        if ($hasMissingClockoutDeductions) {
            $missingClockoutDeduction = $this->attendance->missingClockoutDeductions[0];
            $deductions[] = [
                'type' => 'missing_clockout_deduction',
                'id' => $missingClockoutDeduction->id,
                'status' => $missingClockoutDeduction->status,
                'base_deduction_value' => $missingClockoutDeduction->deduction_value,
                'updated_deduction_value' => $missingClockoutDeduction->updated_value,
                'number_of_incidents' => $this->whenAttributeExists('number_of_missing_clockout_incidents'),
                // TODO: Missing clockout deduction doesn't have approval cycle, edit request, etc. yet
            ];
        }

        if ($hasEarlyClockOutDeductions) {
            $earlyClockOutDeduction = $this->attendance->attendanceEarlyClockOutDeduction;
            $deductions[] = [
                'type' => 'early_clockout_deduction',
                'id' => $earlyClockOutDeduction->id,
                'status' => $earlyClockOutDeduction->status,
                'base_deduction_value' => $earlyClockOutDeduction->deduction_value,
                'updated_deduction_value' => $earlyClockOutDeduction->updated_value,
                'number_of_incidents' => $this->whenAttributeExists('number_of_early_clockout_incidents'),
                'approval_cycle' => isset($this->attendance->attendanceEarlyClockOutDeduction) &&
                isset($this->attendance->attendanceEarlyClockOutDeduction->employeeApproves) ?
                    new ApprovalCycleCollection($this->getApprovalCycle($this->attendance->attendanceEarlyClockOutDeduction, $this->attendance->attendanceEarlyClockOutDeduction->employee))
                    : null,
                'edit_request' => $this->whenAttributeExists('early_clockout_edit_request'),
                'worked_hours' => $this->whenAttributeExists('worked_hours'),
                'duration' => $this->whenAttributeExists('early_clockout_duration'),
                'actionable' => isset($this->attendance->attendanceEarlyClockOutDeduction) &&
                    isset($this->attendance->attendanceEarlyClockOutDeduction->employeeApproves)
                    && $this->attendance->attendanceEarlyClockOutDeduction->status == 'applied'
                    && $this->isActionable($this->attendance->attendanceEarlyClockOutDeduction->employeeApproves, $this->attendance->attendanceEarlyClockOutDeduction->employeeRequest?->status),
                'can_cancel' => isset($this->attendance->attendanceEarlyClockOutDeduction) &&
                    isset($this->attendance->attendanceEarlyClockOutDeduction->employeeApproves) &&
                    $this->canCancel($this->attendance->attendanceEarlyClockOutDeduction->employeeApproves, $this->attendance->attendanceEarlyClockOutDeduction->employeeRequest?->status),
            ];
        }

        return $deductions;
    }
}
